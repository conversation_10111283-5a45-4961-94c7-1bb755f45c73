CREATE TABLE student_game_level_history
(
    id          UUID PRIMARY KEY,
    student_id  UUID NOT NULL,
    level       VARCHAR(255) NOT NULL,
    achieved_at TIMESTAMP NOT NULL,
    shown       BOOL<PERSON>N NOT NULL DEFAULT FALSE,
    CONSTRAINT "student_game_level_history__student_fk" FOREIGN KEY (student_id) REFERENCES student (id)
);

CREATE INDEX "student_game_level_history__student_id_idx" ON student_game_level_history (student_id);