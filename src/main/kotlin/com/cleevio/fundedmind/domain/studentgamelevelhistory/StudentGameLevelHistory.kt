package com.cleevio.fundedmind.domain.studentgamelevelhistory

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.domain.DomainEntity
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.Instant
import java.util.UUID

@Table(name = "student_game_level_history")
@Entity
@DynamicUpdate
class StudentGameLevelHistory private constructor(
    id: UUID,
    val studentId: UUID,
    val level: GameLevel,
    val achievedAt: Instant,
    shown: Boolean,
) : DomainEntity(id) {

    var shown: Boolean = shown
        private set

    companion object {
        fun newStudentGameLevelHistory(
            id: UUID = UUIDv7.randomUUID(),
            studentId: UUID,
            level: GameLevel,
            achievedAt: Instant,
            shown: Boolean = false,
        ) = StudentGameLevelHistory(
            id = id,
            studentId = studentId,
            level = level,
            achievedAt = achievedAt,
            shown = shown,
        )
    }

    fun markAsShown() {
        this.shown = true
    }
}

@Repository
interface StudentGameLevelHistoryRepository : JpaRepository<StudentGameLevelHistory, UUID> {
    fun findByStudentIdOrderByAchievedAtDesc(studentId: UUID): List<StudentGameLevelHistory>
    fun findByStudentIdAndLevel(studentId: UUID, level: GameLevel): StudentGameLevelHistory?
}