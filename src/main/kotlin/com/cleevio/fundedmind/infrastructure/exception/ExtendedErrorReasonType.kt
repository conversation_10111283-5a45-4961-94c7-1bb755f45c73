package com.cleevio.fundedmind.infrastructure.exception

import com.cleevio.library.exceptionhandler.service.model.ErrorCode
import com.cleevio.library.exceptionhandler.service.model.ErrorReason

enum class ExtendedErrorReasonType : ErrorReason {
    INTEGRATION_ERROR,
    ENTITY_IS_DELETED,

    FILE_NOT_FOUND,
    FILE_NOT_DELETED,
    FILE_NOT_COPIED,
    FILE_NOT_SAVED,
    DOWNLOAD_FAILED_EXCEPTION,

    IMAGE_UPLOAD_FAILED,
    IMAGE_DELETE_FAILED,

    INVALID_TOKEN,
    INVALID_API_KEY,
    INVALID_SIGNATURE,
    ACCESS_DENIED,

    USER_NOT_FOUND,
    VERIFICATION_CODE_NOT_FOUND,
    VERIFICATION_CODE_ALREADY_EXISTS,
    VERIFICATION_CODE_HAS_WRONG_STATUS,
    USER_HUBSPOT_IDENTIFIER_NOT_FOUND,
    USER_WITH_EMAIL_ALREADY_EXISTS,
    USER_HAS_WRONG_ROLE,
    USER_HAS_WRONG_ACTIVE_STATUS,
    USER_IS_NOT_TRACKED_IN_PAYMENT_SYSTEM,
    FAILED_TO_CREATE_USER_IN_FIREBASE,
    USER_HAS_DISABLED_NETWORKING,
    USER_IS_ADMIN,

    ONBOARDING_NOT_FOUND,
    ONBOARDING_NOT_YET_FINISHED,
    CANNOT_UPGRADE_ONBOARDING_TIER,

    STUDENT_NOT_FOUND,
    STUDENT_ALREADY_ONBOARDED,
    CANNOT_UPGRADE_STUDENT_TIER,
    STUDENT_HAS_NO_ACCESS_TO_MENTORING,
    STUDENT_HAS_NO_ACTIVE_DISCORD,
    STUDENT_HAS_WRONG_STUDENT_TIER,
    CANNOT_BUY_DISCORD_SUBSCRIPTION,

    STUDENT_TIER_MUST_BE_INVITED,
    DISCORD_USERS_MUST_BE_INVITED,

    STUDENT_DISCORD_NOT_FOUND,
    STUDENT_DISCORD_ALREADY_CONNECTED,

    TRADER_NOT_FOUND,
    TRADER_ALREADY_EXISTS,
    TRADER_CALENDLY_USER_URI_ALREADY_TAKEN,
    TRADER_HAS_SALEABLE_PRODUCT,
    TRADER_HAS_UNFINISHED_MENTORING,
    TRADER_HAS_MENTORING_MEETING_TO_ATTEND,
    TRADER_HAS_NO_SALEABLE_PRODUCT,
    TRADER_ORDER_CANNOT_BE_NEGATIVE,
    ACTIVE_TRADERS_MISMATCH,
    TRADER_WRONG_CALENDLY_DATA,
    TRADER_ACCOUNT_NOT_ACTIVE,
    TRADER_HUBSPOT_PROPERTY_NAME_MISSING,

    PRODUCT_NOT_FOUND,
    PRODUCT_PRICE_NOT_FOUND,
    PRODUCT_ALREADY_EXISTS,
    PRODUCT_IS_SALEABLE,
    PRODUCT_IS_NOT_SALEABLE,
    PRODUCT_NOT_RELATED_TO_TRADER,
    PRODUCT_CANNOT_BE_UPDATED,
    PRODUCT_RELATED_TO_UNFINISHED_MENTORING,
    PRODUCT_RELATED_TO_MENTORING_MEETING_IN_FUTURE,

    TRADER_IN_MEETING_NOT_FOUND,
    MEETING_NOT_FOUND,
    MEETING_INCORRECT_TIME,

    HIGHLIGHT_NOT_FOUND,
    HIGHLIGHT_BUTTON_WITHOUT_LINK,
    HIGHLIGHT_PICTURE_MISSING,
    HIGHLIGHT_ORDER_CANNOT_BE_NEGATIVE,
    ACTIVE_HIGHLIGHTS_MISMATCH,

    COURSE_NOT_FOUND,
    COURSE_IS_LOCKED_FOR_USER,
    COURSE_PICTURE_MISSING,
    COURSE_ORDER_CANNOT_BE_NEGATIVE,
    COURSE_HOMEPAGE_REQUIRES_PUBLIC,
    ACTIVE_COURSES_MISMATCH,
    COURSE_ATTACHMENTS_NOT_ACCESSIBLE_TO_STUDENT,

    COURSE_MODULE_NOT_FOUND,
    COURSE_MODULE_PICTURE_MISSING,
    COURSE_MODULE_ORDER_CANNOT_BE_NEGATIVE,
    ACTIVE_COURSE_MODULES_MISMATCH,
    COURSE_MODULE_CANNOT_HAVE_BOTH_REWARD_ACTIONS,
    COURSE_MODULE_NOT_RELATED_TO_COURSE,
    COURSE_MODULE_NOT_FINISHED,

    LESSON_NOT_FOUND,
    LESSON_ATTACHMENT_NOT_FOUND,
    LESSON_ATTACHMENT_DOCUMENT_NOT_FOUND,
    LESSON_ORDER_CANNOT_BE_NEGATIVE,
    ACTIVE_LESSONS_MISMATCH,
    LESSON_NOT_RELATED_TO_MODULE,

    LESSON_PROGRESS_NOT_FOUND,
    COURSE_MODULE_PROGRESS_NOT_FOUND,
    COURSE_PROGRESS_NOT_FOUND,

    THREAD_COMMENT_NOTIFICATION_NOT_FOUND,
    COMMENT_NOT_FOUND,
    COMMENT_LIKE_NOT_FOUND,
    COMMENT_OWNER_IS_WRONG,

    MENTORING_NOT_FOUND,
    MENTORING_PRICE_NOT_FOUND,
    MENTORING_ALREADY_EXISTS,
    MENTORING_HAS_NO_SESSIONS_LEFT,
    MENTORING_SESSION_USAGE_IS_NEGATIVE,

    REFERRAL_PICTURE_MISSING,
    REFERRAL_ORDER_CANNOT_BE_NEGATIVE,
    REFERRAL_NOT_FOUND,
    ACTIVE_REFERRALS_MISMATCH,

    MENTORING_MEETING_NOT_FOUND,
    MENTORING_MEETING_INCORRECT_TIME,
    MENTORING_MEETING_NOT_ACCESSIBLE,
    MENTORING_MEETING_ALREADY_MODIFIED,
    MENTORING_MEETING_ALREADY_EXISTS,

    NETWORKING_MESSAGE_NOT_FOUND,
    NETWORKING_MESSAGE_NOT_ACCESSIBLE,

    GAME_LEVEL_REWARD_NOT_FOUND,
    GAME_LEVEL_REWARD_PICTURE_MISSING,
    GAME_LEVEL_REWARD_ORDER_CANNOT_BE_NEGATIVE,
    GAME_LEVEL_REWARD_BUTTON_WITHOUT_LINK,
    ACTIVE_GAME_LEVEL_REWARDS_MISMATCH,

    GAME_DOCUMENT_NOT_FOUND,
    GAME_DOCUMENT_TRUTH_SCORE_CANNOT_BE_NEGATIVE,
    GAME_DOCUMENT_PAYOUT_AMOUNT_CANNOT_BE_NEGATIVE,
    GAME_DOCUMENT_PAYOUT_AMOUNT_IS_REQUIRED,

    GAME_PAYOUT_SETTINGS_NOT_FOUND,

    STUDENT_GAME_LEVEL_HISTORY_NOT_FOUND,

    USER_LOCATION_NOT_FOUND,
    ;

    override val errorCode: ErrorCode
        get() = ErrorCode(this.name)
}
