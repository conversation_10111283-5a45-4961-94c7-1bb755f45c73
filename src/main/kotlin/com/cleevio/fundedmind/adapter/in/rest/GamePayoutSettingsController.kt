package com.cleevio.fundedmind.adapter.`in`.rest

import com.cleevio.fundedmind.adapter.`in`.ApiVersion
import com.cleevio.fundedmind.adapter.`in`.SwaggerBearerToken
import com.cleevio.fundedmind.adapter.`in`.rest.request.AdminPatchesGamePayoutSettingsRequest
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.common.query.QueryBus
import com.cleevio.fundedmind.application.module.gamepayoutsettings.query.GetGamePayoutSettingsQuery
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.annotation.security.RolesAllowed
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.time.Year

@Tag(name = "game payout settings [Admin]")
@RestController
@SwaggerBearerToken
@RequestMapping("/game-payout-offset")
class GamePayoutSettingsController(
    private val commandBus: CommandBus,
    private val queryBus: QueryBus,
) {

    @Operation(
        description = """
            Get current year game payout settings.
            404 - game payout settings for the current year not found.
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @GetMapping("/current", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getCurrentGamePayoutSettings(): GetGamePayoutSettingsQuery.Result =
        queryBus(GetGamePayoutSettingsQuery(year = Year.now()))

    @Operation(
        description = """
            Get game payout settings for the specified year.
            404 - game payout settings for the specified year not found.
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @GetMapping("/{year}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getGamePayoutSettings(@PathVariable year: Int): GetGamePayoutSettingsQuery.Result =
        queryBus(GetGamePayoutSettingsQuery(year = Year.of(year)))

    @Operation(
        description = """
            Admin patches current game payout settings.
            404 - game payout settings for the specified year not found. (should not happen)
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PatchMapping("/current", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun adminPatchesCurrentGamePayoutSettings(@RequestBody request: AdminPatchesGamePayoutSettingsRequest): Unit =
        commandBus(request.toCommand())

    @Operation(
        description = """
            Admin patches specific game payout settings.
            404 - game payout settings for the specified year not found.
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PatchMapping("/{year}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun adminPatchesGamePayoutSettings(
        @RequestBody request: AdminPatchesGamePayoutSettingsRequest,
        @PathVariable year: Int,
    ): Unit = commandBus(request.toCommand(year = Year.of(year)))
}
