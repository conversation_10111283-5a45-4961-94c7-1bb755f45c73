package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.gamepayoutsettings.command.AdminPatchesGamePayoutSettingsCommand
import java.math.BigDecimal
import java.time.Year

data class AdminPatchesGamePayoutSettingsRequest(
    val offset: BigDecimal,
) {
    fun toCommand(year: Year = Year.now()) = AdminPatchesGamePayoutSettingsCommand(
        year = year,
        offset = offset,
    )
}
