package com.cleevio.fundedmind.application.module.studentgamelevelhistory.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.application.module.studentgamelevelhistory.exception.StudentGameLevelHistoryNotFoundException
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.studentgamelevelhistory.StudentGameLevelHistory
import com.cleevio.fundedmind.domain.studentgamelevelhistory.StudentGameLevelHistoryRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
@Transactional(readOnly = true)
class StudentGameLevelHistoryFinderService(
    private val studentGameLevelHistoryRepository: StudentGameLevelHistoryRepository,
) : BaseFinderService<StudentGameLevelHistory>(studentGameLevelHistoryRepository) {

    override fun errorBlock(message: String) = throw StudentGameLevelHistoryNotFoundException(message)

    override fun getEntityType() = StudentGameLevelHistory::class

    fun findByStudentId(studentId: UUID): List<StudentGameLevelHistory> =
        studentGameLevelHistoryRepository.findByStudentIdOrderByAchievedAtDesc(studentId)

    fun findByStudentIdAndLevel(studentId: UUID, level: GameLevel): StudentGameLevelHistory? =
        studentGameLevelHistoryRepository.findByStudentIdAndLevel(studentId, level)

    fun getByStudentIdAndLevel(studentId: UUID, level: GameLevel): StudentGameLevelHistory =
        findByStudentIdAndLevel(studentId, level)
            ?: throw StudentGameLevelHistoryNotFoundException("StudentGameLevelHistory for studentId: '$studentId' and level: '$level' not found.")
}