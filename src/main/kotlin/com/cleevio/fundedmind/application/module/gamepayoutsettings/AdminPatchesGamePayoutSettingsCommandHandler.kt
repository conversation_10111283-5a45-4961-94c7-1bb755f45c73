package com.cleevio.fundedmind.application.module.gamepayoutsettings

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.module.gamepayoutsettings.command.AdminPatchesGamePayoutSettingsCommand
import com.cleevio.fundedmind.application.module.gamepayoutsettings.finder.GamePayoutSettingsFinderService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AdminPatchesGamePayoutSettingsCommandHandler(
    private val gamePayoutSettingsFinderService: GamePayoutSettingsFinderService,
) : CommandHandler<Unit, AdminPatchesGamePayoutSettingsCommand> {
    override val command = AdminPatchesGamePayoutSettingsCommand::class

    @Transactional
    override fun handle(command: AdminPatchesGamePayoutSettingsCommand) {
        gamePayoutSettingsFinderService
            .getByYear(command.year)
            .updatePayoutOffset(command.offset)
    }
}
