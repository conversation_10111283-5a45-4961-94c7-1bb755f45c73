package com.cleevio.fundedmind.application.module.studentgamelevelhistory.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class StudentGameLevelHistoryNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.STUDENT_GAME_LEVEL_HISTORY_NOT_FOUND,
    message = message,
)